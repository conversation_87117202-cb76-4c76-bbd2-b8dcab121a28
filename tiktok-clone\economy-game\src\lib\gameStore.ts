'use client'

import { useState, useEffect } from 'react'

// Simple types for the game
export interface GameResources {
  money: number
  gems: number
  energy: number
  experience: number
  level: number
}

export interface Achievement {
  id: string
  title: string
  description: string
  reward: {
    money?: number
    gems?: number
    experience?: number
  }
  isCompleted: boolean
  progress: number
  target: number
}

// Simple game store without persistence for now
let gameState = {
  resources: {
    money: 1000,
    gems: 10,
    energy: 100,
    experience: 0,
    level: 1
  },
  bank: {
    balance: 0,
    savingsBalance: 0,
    loanAmount: 0,
    interestRate: 0.05,
    lastInterestUpdate: Date.now()
  },
  investments: [],
  availableJobs: [
    {
      id: 'delivery',
      title: 'توصيل الطلبات',
      hourlyRate: 50,
      energyCost: 10,
      experienceGain: 5,
      unlockLevel: 1,
      isUnlocked: true
    }
  ],
  currentJob: null,
  achievements: [
    {
      id: 'first_1000',
      title: 'أول ألف',
      description: 'اجمع 1000 دولار',
      reward: {
        gems: 5
      },
      isCompleted: false,
      progress: 0,
      target: 1000
    },
    {
      id: 'level_5',
      title: 'المستوى الخامس',
      description: 'وصل للمستوى 5',
      reward: {
        money: 500,
        gems: 10
      },
      isCompleted: false,
      progress: 0,
      target: 5
    },
    {
      id: 'first_job',
      title: 'أول وظيفة',
      description: 'اعمل في وظيفتك الأولى',
      reward: {
        experience: 50,
        money: 200
      },
      isCompleted: false,
      progress: 0,
      target: 1
    }
  ] as Achievement[],
  upgrades: [],
  lastSaveTime: Date.now(),
  offlineEarnings: 0
}

export const useGameStore = () => {
  const [state, setState] = useState(gameState)

  const updateState = (newState: any) => {
    gameState = { ...gameState, ...newState }
    setState(gameState)
  }

  return {
    ...state,
    addMoney: (amount: number) => {
      updateState({
        resources: {
          ...state.resources,
          money: state.resources.money + amount
        }
      })
    },
    spendMoney: (amount: number) => {
      if (state.resources.money >= amount) {
        updateState({
          resources: {
            ...state.resources,
            money: state.resources.money - amount
          }
        })
        return true
      }
      return false
    },
    addGems: (amount: number) => {
      updateState({
        resources: {
          ...state.resources,
          gems: state.resources.gems + amount
        }
      })
    },
    spendGems: (amount: number) => {
      if (state.resources.gems >= amount) {
        updateState({
          resources: {
            ...state.resources,
            gems: state.resources.gems - amount
          }
        })
        return true
      }
      return false
    },
    addExperience: (amount: number) => {
      const newExp = state.resources.experience + amount
      const newLevel = Math.floor(newExp / 100) + 1

      updateState({
        resources: {
          ...state.resources,
          experience: newExp,
          level: newLevel
        }
      })
    },
    useEnergy: (amount: number) => {
      if (state.resources.energy >= amount) {
        updateState({
          resources: {
            ...state.resources,
            energy: Math.max(0, state.resources.energy - amount)
          }
        })
        return true
      }
      return false
    },
    regenerateEnergy: () => {
      updateState({
        resources: {
          ...state.resources,
          energy: Math.min(100, state.resources.energy + 2)
        }
      })
    },
    calculateOfflineEarnings: () => {},
    calculateInterest: () => {},
    workJob: (jobId: string) => false,
    depositMoney: (amount: number) => {},
    withdrawMoney: (amount: number) => false,
    takeLoan: (amount: number) => {},
    payLoan: (amount: number) => false,
    buyInvestment: () => {},
    sellInvestment: () => {},
    updateInvestmentValues: () => {},
    unlockJob: () => {},
    checkAchievements: () => {},
    claimAchievement: () => {},
    purchaseUpgrade: () => false,
    saveGame: () => {},
    resetGame: () => {}
  }
}
