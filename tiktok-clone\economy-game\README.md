# 🎮 لعبة الاقتصاد - Economy Game

تطبيق ويب تفاعلي للعبة اقتصادية لجمع الأموال والجواهر مع نظام بنك متكامل، مصمم خصيصاً للهواتف المحمولة والحاسوب.

## 🌟 المميزات الرئيسية

### 💰 نظام الموارد
- **الأموال**: العملة الأساسية للعبة
- **الجواهر**: العملة النادرة للمشتريات الخاصة
- **الطاقة**: مطلوبة لأداء الأنشطة (تتجدد تلقائياً)
- **الخبرة والمستويات**: نظام تطوير الشخصية

### 🏦 النظام المصرفي
- **حساب البنك**: إيداع وسحب الأموال
- **نظام القروض**: اقتراض الأموال مع فوائد
- **الفوائد**: كسب فوائد على الودائع
- **الاستثمارات**: (قيد التطوير)

### 💼 نظام الوظائف
- **وظائف متنوعة**: من التوصيل إلى البرمجة
- **متطلبات المستوى**: فتح وظائف جديدة بالتطور
- **نظام الطاقة**: كل وظيفة تستهلك طاقة
- **كسب الخبرة**: تطوير المهارات والمستوى

### 🛒 المتجر والترقيات
- **ترقيات الطاقة**: زيادة سرعة استعادة الطاقة
- **ترقيات الإنتاجية**: زيادة الدخل من الوظائف
- **ترقيات البنك**: تحسين معدلات الفائدة
- **عناصر خاصة**: ترقيات نادرة بالجواهر

### 🏆 نظام الإنجازات
- **تحديات متنوعة**: أهداف قصيرة وطويلة المدى
- **مكافآت قيمة**: أموال وجواهر وخبرة
- **تتبع التقدم**: مؤشرات بصرية للإنجاز
- **تحديات يومية**: (قيد التطوير)

### 📱 تحسين للهواتف المحمولة
- **تصميم متجاوب**: يعمل على جميع أحجام الشاشات
- **PWA**: يمكن تثبيته كتطبيق على الهاتف
- **تفاعلات اللمس**: محسن للتحكم باللمس
- **واجهة عربية**: دعم كامل للغة العربية (RTL)

## 🚀 التشغيل السريع

### المتطلبات
- Node.js 18 أو أحدث
- npm أو yarn

### خطوات التثبيت

1. **تثبيت المكتبات**
```bash
npm install
```

2. **تشغيل التطبيق**
```bash
npm run dev
```

3. **فتح التطبيق**
افتح [http://localhost:3000](http://localhost:3000) في المتصفح

## 🎯 كيفية اللعب

### البداية
- تبدأ بـ $1000 و 10 جواهر و 100 طاقة
- المستوى الأول مع 0 نقطة خبرة

### كسب المال
1. **العمل**: اختر وظيفة من قائمة الوظائف
2. **البنك**: أودع أموالك لكسب فوائد
3. **الاستثمار**: (قريباً) استثمر في أسواق مختلفة

### التطوير
1. **اكسب الخبرة**: من خلال العمل والأنشطة
2. **ارتق في المستوى**: كل 100 نقطة خبرة = مستوى جديد
3. **افتح وظائف جديدة**: وظائف أفضل تتطلب مستويات أعلى

## 🛠️ التقنيات المستخدمة

- **Frontend**: Next.js 15 + TypeScript
- **Styling**: Tailwind CSS
- **State Management**: React Hooks
- **PWA**: Web App Manifest
- **Icons**: Emoji + CSS
- **Animations**: CSS Transitions

## 📱 تثبيت كـ PWA

### على Android
1. افتح التطبيق في Chrome
2. اضغط على قائمة المتصفح (⋮)
3. اختر "إضافة إلى الشاشة الرئيسية"

### على iOS
1. افتح التطبيق في Safari
2. اضغط على زر المشاركة
3. اختر "إضافة إلى الشاشة الرئيسية"

## 🎮 نصائح اللعب

### للمبتدئين
- ابدأ بوظيفة "توصيل الطلبات" لكسب المال الأولي
- أودع أموالك في البنك لكسب فوائد
- اشتر ترقية "مشروب الطاقة" أولاً

### للمتقدمين
- وازن بين العمل والاستثمار
- استخدم القروض بحذر (فوائد مضاعفة)
- ركز على الوظائف عالية الأجر

## 🔄 التحديثات المستقبلية

### قريباً
- [ ] نظام الاستثمار الكامل
- [ ] تحديات يومية
- [ ] نظام الأصدقاء والمنافسة
- [ ] المزيد من الوظائف والترقيات

## 🎉 استمتع باللعب!

نتمنى لك تجربة ممتعة في بناء إمبراطوريتك الاقتصادية! 💰🏆
